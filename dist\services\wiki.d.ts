import { CallToolResult } from "@modelcontextprotocol/sdk/types.js";
/**
 * Axmol Wiki 服务
 * 负责从官方 Wiki 和文档源检索信息
 */
export declare class AxmolWikiService {
    private readonly AXMOL_WIKI_BASE;
    private readonly AXMOL_DOCS_BASE;
    /**
     * 获取精灵渲染相关文档
     */
    getSpriteDocs(topic: string, language?: string): Promise<CallToolResult>;
    /**
     * 搜索文档内容
     */
    private searchDocumentation;
    /**
     * 生成代码示例
     */
    private generateSpriteExamples;
}
//# sourceMappingURL=wiki.d.ts.map