/**
 * Axmol MCP 服务器核心类
 * 统一管理所有 Axmol 相关的功能和服务
 */
export class AxmolMCPServer {
    constructor() {
        // 初始化所有服务
    }
    /**
     * 获取所有可用的工具列表
     */
    async getTools() {
        return [
            {
                name: "axmol_get_sprite_docs",
                description: "从 Axmol 官方 Wiki 检索渲染精灵相关的文档和代码示例",
                inputSchema: {
                    type: "object",
                    properties: {
                        topic: {
                            type: "string",
                            description: "要搜索的精灵相关主题",
                        },
                        language: {
                            type: "string",
                            enum: ["cpp", "lua", "all"],
                            default: "cpp",
                            description: "代码示例语言",
                        },
                    },
                    required: ["topic"],
                },
            },
            {
                name: "axmol_generate_character_movement",
                description: "生成 Axmol 2.6.0 角色移动的 C++ 代码示例",
                inputSchema: {
                    type: "object",
                    properties: {
                        movementType: {
                            type: "string",
                            enum: ["keyboard", "touch", "physics", "pathfinding"],
                            description: "移动控制类型",
                        },
                        characterType: {
                            type: "string",
                            enum: ["sprite", "node", "physics_body"],
                            description: "角色对象类型",
                        },
                    },
                    required: ["movementType", "characterType"],
                },
            },
            {
                name: "axmol_list_physics_functions",
                description: "列出 Axmol 物理碰撞检测相关的函数及其调用方法",
                inputSchema: {
                    type: "object",
                    properties: {
                        category: {
                            type: "string",
                            enum: ["collision", "rigidbody", "joints", "world", "all"],
                            default: "all",
                            description: "物理功能类别",
                        },
                        includeExamples: {
                            type: "boolean",
                            default: true,
                            description: "是否包含使用示例",
                        },
                    },
                },
            },
            {
                name: "axmol_game_development_workflow",
                description: "总结 Axmol 游戏开发的标准流程和最佳实践",
                inputSchema: {
                    type: "object",
                    properties: {
                        projectType: {
                            type: "string",
                            enum: ["2d_game", "3d_game", "mobile_game", "desktop_game"],
                            description: "项目类型",
                        },
                        complexity: {
                            type: "string",
                            enum: ["beginner", "intermediate", "advanced"],
                            default: "intermediate",
                            description: "复杂度级别",
                        },
                    },
                    required: ["projectType"],
                },
            },
            {
                name: "axmol_animation_tutorial",
                description: "整理 Axmol 动画系统的使用教程和最佳实践",
                inputSchema: {
                    type: "object",
                    properties: {
                        animationType: {
                            type: "string",
                            enum: ["sprite_animation", "skeletal_animation", "tween_animation", "particle_animation", "all"],
                            default: "all",
                            description: "动画类型",
                        },
                        includeOptimization: {
                            type: "boolean",
                            default: true,
                            description: "是否包含性能优化建议",
                        },
                    },
                },
            },
            {
                name: "axmol_event_listener_examples",
                description: "提取 Axmol 事件监听机制的代码示例",
                inputSchema: {
                    type: "object",
                    properties: {
                        eventType: {
                            type: "string",
                            enum: ["touch", "keyboard", "mouse", "custom", "all"],
                            default: "all",
                            description: "事件类型",
                        },
                        platform: {
                            type: "string",
                            enum: ["android", "ios", "windows", "linux", "all"],
                            default: "all",
                            description: "目标平台",
                        },
                    },
                },
            },
            {
                name: "axmol_analyze_common_errors",
                description: "分析 Axmol 开发中的常见错误及解决方案",
                inputSchema: {
                    type: "object",
                    properties: {
                        errorCategory: {
                            type: "string",
                            enum: ["compilation", "runtime", "memory", "rendering", "physics", "all"],
                            default: "all",
                            description: "错误类别",
                        },
                        severity: {
                            type: "string",
                            enum: ["critical", "warning", "info", "all"],
                            default: "all",
                            description: "错误严重程度",
                        },
                    },
                },
            },
            {
                name: "axmol_cocos2dx_migration",
                description: "对比 Cocos2d-x 和 Axmol 的 API 差异，提供迁移建议",
                inputSchema: {
                    type: "object",
                    properties: {
                        apiCategory: {
                            type: "string",
                            enum: ["rendering", "audio", "physics", "ui", "scripting", "all"],
                            default: "all",
                            description: "API 类别",
                        },
                        cocos2dxVersion: {
                            type: "string",
                            enum: ["3.x", "4.x"],
                            default: "4.x",
                            description: "Cocos2d-x 版本",
                        },
                    },
                },
            },
            {
                name: "axmol_generate_module_docs",
                description: "自动生成 Axmol 模块开发文档大纲",
                inputSchema: {
                    type: "object",
                    properties: {
                        moduleName: {
                            type: "string",
                            description: "模块名称",
                        },
                        moduleType: {
                            type: "string",
                            enum: ["rendering", "audio", "physics", "ui", "network", "custom"],
                            description: "模块类型",
                        },
                        includeAPI: {
                            type: "boolean",
                            default: true,
                            description: "是否包含 API 参考",
                        },
                    },
                    required: ["moduleName", "moduleType"],
                },
            },
            {
                name: "axmol_ai_integration_guide",
                description: "提供第三方 AI 库与 Axmol 集成的步骤和示例",
                inputSchema: {
                    type: "object",
                    properties: {
                        aiLibrary: {
                            type: "string",
                            enum: ["tensorflow", "pytorch", "opencv", "custom"],
                            description: "AI 库类型",
                        },
                        platform: {
                            type: "string",
                            enum: ["android", "ios", "windows", "linux", "all"],
                            default: "all",
                            description: "目标平台",
                        },
                        useCase: {
                            type: "string",
                            enum: ["image_recognition", "nlp", "game_ai", "computer_vision"],
                            description: "使用场景",
                        },
                    },
                    required: ["aiLibrary", "useCase"],
                },
            },
            {
                name: "axmol_generate_changelog",
                description: "生成 Axmol 引擎更新日志摘要",
                inputSchema: {
                    type: "object",
                    properties: {
                        version: {
                            type: "string",
                            description: "目标版本号，如 '2.6.0'",
                        },
                        includeBreakingChanges: {
                            type: "boolean",
                            default: true,
                            description: "是否包含破坏性变更",
                        },
                        format: {
                            type: "string",
                            enum: ["markdown", "json", "text"],
                            default: "markdown",
                            description: "输出格式",
                        },
                    },
                    required: ["version"],
                },
            },
            {
                name: "axmol_memory_best_practices",
                description: "检索 Axmol 内存管理的最佳实践和优化建议",
                inputSchema: {
                    type: "object",
                    properties: {
                        memoryType: {
                            type: "string",
                            enum: ["texture", "audio", "scene", "object", "all"],
                            default: "all",
                            description: "内存管理类型",
                        },
                        platform: {
                            type: "string",
                            enum: ["mobile", "desktop", "all"],
                            default: "all",
                            description: "目标平台",
                        },
                    },
                },
            },
            {
                name: "axmol_lua_sprite_animation",
                description: "生成使用 Lua 脚本控制精灵动画的示例代码",
                inputSchema: {
                    type: "object",
                    properties: {
                        animationType: {
                            type: "string",
                            enum: ["frame_animation", "tween_animation", "skeletal_animation"],
                            description: "动画类型",
                        },
                        complexity: {
                            type: "string",
                            enum: ["simple", "intermediate", "advanced"],
                            default: "intermediate",
                            description: "示例复杂度",
                        },
                    },
                    required: ["animationType"],
                },
            },
            {
                name: "axmol_collect_new_features",
                description: "搜集 Axmol 2.6.0 的新功能和改动信息",
                inputSchema: {
                    type: "object",
                    properties: {
                        category: {
                            type: "string",
                            enum: ["rendering", "audio", "physics", "ui", "scripting", "performance", "all"],
                            default: "all",
                            description: "功能类别",
                        },
                        includeDeprecated: {
                            type: "boolean",
                            default: true,
                            description: "是否包含已弃用的功能",
                        },
                    },
                },
            },
            {
                name: "axmol_beginner_guide",
                description: "整理 Axmol 新手开发指南和入门教程",
                inputSchema: {
                    type: "object",
                    properties: {
                        learningPath: {
                            type: "string",
                            enum: ["quick_start", "step_by_step", "project_based"],
                            default: "step_by_step",
                            description: "学习路径",
                        },
                        background: {
                            type: "string",
                            enum: ["no_programming", "some_programming", "game_development"],
                            default: "some_programming",
                            description: "学习者背景",
                        },
                    },
                },
            },
        ];
    }
    /**
     * 处理工具调用
     */
    async handleToolCall(params) {
        const { name, arguments: args } = params;
        try {
            switch (name) {
                case "axmol_get_sprite_docs":
                    return this.getSpriteDocs(args.topic, args.language);
                case "axmol_generate_character_movement":
                    return this.generateCharacterMovement(args.movementType, args.characterType);
                case "axmol_list_physics_functions":
                    return this.physicsService.listPhysicsFunctions(args.category, args.includeExamples);
                case "axmol_game_development_workflow":
                    return this.workflowService.getGameDevelopmentWorkflow(args.projectType, args.complexity);
                case "axmol_animation_tutorial":
                    return this.animationService.getAnimationTutorial(args.animationType, args.includeOptimization);
                case "axmol_event_listener_examples":
                    return this.eventService.getEventListenerExamples(args.eventType, args.platform);
                case "axmol_analyze_common_errors":
                    return this.errorService.analyzeCommonErrors(args.errorCategory, args.severity);
                case "axmol_cocos2dx_migration":
                    return this.migrationService.getCocos2dxMigrationGuide(args.apiCategory, args.cocos2dxVersion);
                case "axmol_generate_module_docs":
                    return this.documentationService.generateModuleDocs(args.moduleName, args.moduleType, args.includeAPI);
                case "axmol_ai_integration_guide":
                    return this.integrationService.getAIIntegrationGuide(args.aiLibrary, args.platform, args.useCase);
                case "axmol_generate_changelog":
                    return this.updateService.generateChangelog(args.version, args.includeBreakingChanges, args.format);
                case "axmol_memory_best_practices":
                    return this.memoryService.getMemoryBestPractices(args.memoryType, args.platform);
                case "axmol_lua_sprite_animation":
                    return this.luaService.generateLuaSpriteAnimation(args.animationType, args.complexity);
                case "axmol_collect_new_features":
                    return this.featureService.collectNewFeatures(args.category, args.includeDeprecated);
                case "axmol_beginner_guide":
                    return this.guideService.getBeginnerGuide(args.learningPath, args.background);
                default:
                    throw new Error(`未知的工具: ${name}`);
            }
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `执行工具 ${name} 时发生错误: ${error instanceof Error ? error.message : String(error)}`,
                    },
                ],
                isError: true,
            };
        }
    }
    getSpriteDocs(topic, language = "cpp") {
        const content = `# Axmol 精灵渲染文档

## 搜索主题: ${topic}

## 文档内容

### 精灵渲染基础

Axmol 中的精灵渲染是游戏开发的核心功能之一。精灵 (Sprite) 是显示 2D 图像的基本组件。

#### 基本概念
- **精灵 (Sprite)**: 可以显示纹理的 2D 对象
- **纹理 (Texture)**: 图像数据，存储在 GPU 内存中
- **精灵帧 (SpriteFrame)**: 纹理的一部分，用于动画

## 代码示例 (${language.toUpperCase()})

\`\`\`cpp
#include "axmol.h"
USING_NS_AX;

// 基本精灵创建和显示
auto sprite = Sprite::create("HelloWorld.png");
if (sprite) {
    sprite->setPosition(Vec2(100, 100));
    sprite->setScale(0.5f);
    this->addChild(sprite, 0);
}
\`\`\`

## 相关 API

### 核心精灵类
- \`ax::Sprite\` - 主要的精灵类
- \`ax::SpriteFrame\` - 精灵帧
- \`ax::Texture2D\` - 纹理类
- \`ax::SpriteBatchNode\` - 批量渲染节点

### 常用方法
- \`Sprite::create()\` - 创建精灵
- \`Sprite::setTexture()\` - 设置纹理
- \`Sprite::setPosition()\` - 设置位置
- \`Sprite::setScale()\` - 设置缩放
`;
        return {
            content: [{ type: "text", text: content }],
        };
    }
    generateCharacterMovement(movementType, characterType) {
        const content = `# Axmol 2.6.0 角色移动代码示例

## 配置信息
- **移动类型**: ${movementType}
- **角色类型**: ${characterType}
- **引擎版本**: Axmol 2.6.0

## 完整代码实现

\`\`\`cpp
#include "axmol.h"
USING_NS_AX;

class CharacterController : public Node {
public:
    static CharacterController* create();
    virtual bool init();
    virtual void update(float deltaTime);
    
private:
    Sprite* m_character;
    Vec2 m_position;
    float m_speed;
    ${this.getMovementSpecificMembers(movementType)}
};

bool CharacterController::init() {
    if (!Node::init()) return false;
    
    // 创建角色精灵
    m_character = Sprite::create("character.png");
    if (m_character) {
        this->addChild(m_character);
        m_position = Vec2(100, 100);
        m_character->setPosition(m_position);
    }
    m_speed = 200.0f;
    
    ${this.getMovementInitCode(movementType)}
    
    this->scheduleUpdate();
    return true;
}

void CharacterController::update(float deltaTime) {
    Node::update(deltaTime);
    ${this.getMovementUpdateCode(movementType)}
}
\`\`\`

## 使用说明

1. 将代码添加到您的场景类中
2. 确保已正确设置输入监听器
3. 根据需要调整移动速度和参数
4. 测试在目标平台上的表现
`;
        return {
            content: [{ type: "text", text: content }],
        };
    }
    getMovementSpecificMembers(movementType) {
        switch (movementType) {
            case "keyboard":
                return `
    bool m_keyPressed[4]; // W, A, S, D
    EventListenerKeyboard* m_keyboardListener;`;
            case "touch":
                return `
    Vec2 m_touchTarget;
    bool m_isTouching;
    EventListenerTouchOneByOne* m_touchListener;`;
            case "physics":
                return `
    PhysicsBody* m_physicsBody;
    Vec2 m_targetVelocity;`;
            case "pathfinding":
                return `
    std::vector<Vec2> m_path;
    int m_currentPathIndex;`;
            default:
                return "";
        }
    }
    getMovementInitCode(movementType) {
        switch (movementType) {
            case "keyboard":
                return `
    // 初始化键盘监听
    for (int i = 0; i < 4; i++) m_keyPressed[i] = false;
    m_keyboardListener = EventListenerKeyboard::create();
    // 设置键盘事件处理...`;
            case "touch":
                return `
    // 初始化触摸监听
    m_isTouching = false;
    m_touchListener = EventListenerTouchOneByOne::create();
    // 设置触摸事件处理...`;
            case "physics":
                return `
    // 初始化物理体
    auto physicsBody = PhysicsBody::createBox(m_character->getContentSize());
    m_character->setPhysicsBody(physicsBody);
    m_physicsBody = physicsBody;`;
            case "pathfinding":
                return `
    // 初始化路径系统
    m_currentPathIndex = 0;`;
            default:
                return "// 基本移动初始化";
        }
    }
    getMovementUpdateCode(movementType) {
        switch (movementType) {
            case "keyboard":
                return `
    // 键盘移动逻辑
    Vec2 movement = Vec2::ZERO;
    if (m_keyPressed[0]) movement.y += 1; // W
    if (m_keyPressed[1]) movement.x -= 1; // A
    if (m_keyPressed[2]) movement.y -= 1; // S
    if (m_keyPressed[3]) movement.x += 1; // D
    
    if (movement.length() > 0) {
        movement.normalize();
        movement *= m_speed * deltaTime;
        m_position += movement;
        m_character->setPosition(m_position);
    }`;
            case "touch":
                return `
    // 触摸移动逻辑
    if (m_isTouching) {
        Vec2 direction = m_touchTarget - m_character->getPosition();
        if (direction.length() > 5.0f) {
            direction.normalize();
            Vec2 movement = direction * m_speed * deltaTime;
            m_position += movement;
            m_character->setPosition(m_position);
        }
    }`;
            case "physics":
                return `
    // 物理移动逻辑
    if (m_targetVelocity.length() > 0) {
        m_physicsBody->setVelocity(m_targetVelocity);
    }`;
            case "pathfinding":
                return `
    // 路径移动逻辑
    if (m_currentPathIndex < m_path.size()) {
        Vec2 target = m_path[m_currentPathIndex];
        Vec2 direction = target - m_character->getPosition();
        if (direction.length() < 5.0f) {
            m_currentPathIndex++;
        } else {
            direction.normalize();
            Vec2 movement = direction * m_speed * deltaTime;
            m_position += movement;
            m_character->setPosition(m_position);
        }
    }`;
            default:
                return "// 基本移动逻辑";
        }
    }
}
//# sourceMappingURL=server.js.map