{"version": 3, "file": "explicit-member-accessibility.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-member-accessibility.ts"], "names": [], "mappings": ";;AACA,oDAA2E;AAC3E,wEAAsE;AAEtE,kCAAwD;AA0BxD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,oCAAoC;SACrC;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,sDAAsD;YACxD,2BAA2B,EACzB,qDAAqD;YACvD,wBAAwB,EAAE,yCAAyC;SACpE;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,kBAAkB,EAAE;wBAClB,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,UAAU,CAAC;gCAClB,WAAW,EAAE,6BAA6B;6BAC3C;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,WAAW,CAAC;gCACnB,WAAW,EAAE,yCAAyC;6BACvD;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,CAAC,KAAK,CAAC;gCACb,WAAW,EAAE,2CAA2C;6BACzD;yBACF;qBACF;iBACF;gBACD,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,aAAa,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE;oBAC7D,SAAS,EAAE;wBACT,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,SAAS,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE;4BACzD,YAAY,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE;4BAC5D,OAAO,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE;4BACvD,UAAU,EAAE,EAAE,IAAI,EAAE,oCAAoC,EAAE;4BAC1D,mBAAmB,EAAE;gCACnB,IAAI,EAAE,oCAAoC;6BAC3C;yBACF;wBAED,oBAAoB,EAAE,KAAK;qBAC5B;oBACD,kBAAkB,EAAE;wBAClB,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;IAC/C,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAuB,MAAM,CAAC,aAAa,IAAI,UAAU,CAAC;QACzE,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC;QACtD,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC;QACvD,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC;QACnD,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC;QACpD,MAAM,cAAc,GAAG,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC;QAClE,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;QAEpE;;;WAGG;QACH,SAAS,gCAAgC,CACvC,gBAA2C;YAE3C,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBACnE,OAAO;YACT,CAAC;YAED,IAAI,QAAQ,GAAG,mBAAmB,CAAC;YACnC,IAAI,KAAK,GAAG,SAAS,CAAC;YACtB,QAAQ,gBAAgB,CAAC,IAAI,EAAE,CAAC;gBAC9B,KAAK,QAAQ;oBACX,KAAK,GAAG,WAAW,CAAC;oBACpB,MAAM;gBACR,KAAK,aAAa;oBAChB,KAAK,GAAG,SAAS,CAAC;oBAClB,MAAM;gBACR,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK;oBACR,KAAK,GAAG,aAAa,CAAC;oBACtB,QAAQ,GAAG,GAAG,gBAAgB,CAAC,IAAI,oBAAoB,CAAC;oBACxD,MAAM;YACV,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAA,wBAAiB,EAC5C,gBAAgB,EAChB,UAAU,CACX,CAAC;YAEF,IAAI,KAAK,KAAK,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1D,OAAO;YACT,CAAC;YAED,IACE,KAAK,KAAK,WAAW;gBACrB,gBAAgB,CAAC,aAAa,KAAK,QAAQ,EAC3C,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,gBAAgB;oBACtB,SAAS,EAAE,6BAA6B;oBACxC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,UAAU;qBACjB;oBACD,GAAG,EAAE,mCAAmC,CAAC,gBAAgB,CAAC;iBAC3D,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,KAAK,KAAK,UAAU,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;gBACnE,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,gBAAgB;oBACtB,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,UAAU;qBACjB;oBACD,OAAO,EAAE,kCAAkC,CAAC,gBAAgB,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED;;WAEG;QACH,SAAS,mCAAmC,CAC1C,IAKgC;YAEhC,OAAO,UAAU,KAAyB;gBACxC,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAI,aAAiC,CAAC;gBACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACvC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxB,IACE,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,OAAO;wBACtC,KAAK,CAAC,KAAK,KAAK,QAAQ,EACxB,CAAC;wBACD,MAAM,yBAAyB,GAC7B,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACrC,IAAI,yBAAyB,CAAC,MAAM,EAAE,CAAC;4BACrC,sCAAsC;4BACtC,UAAU;4BACV,aAAa,GAAG;gCACd,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gCACd,yBAAyB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;6BACtC,CAAC;4BACF,MAAM;wBACR,CAAC;6BAAM,CAAC;4BACN,sBAAsB;4BACtB,UAAU;4BACV,aAAa,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;4BACzD,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAC,WAAW,CAAC,aAAc,CAAC,CAAC;YAC3C,CAAC,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,kCAAkC,CACzC,IAKgC;YAEhC,SAAS,GAAG,CACV,aAAqC,EACrC,KAAyB;gBAEzB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;oBAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAClE,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,aAAa,CAAE,CAAC;oBAC3D,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,aAAa,GAAG,CAAC,CAAC;gBAChE,CAAC;gBACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,aAAa,GAAG,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO;gBACL;oBACE,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACxB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;iBACnC;gBACD;oBACE,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACzB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;iBACpC;gBACD;oBACE,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;oBAC3B,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC;iBACtC;aACF,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,kCAAkC,CACzC,kBAEyC;YAEzC,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBACrE,OAAO;YACT,CAAC;YAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC;YAElC,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,IAAA,wBAAiB,EAC9C,kBAAkB,EAClB,UAAU,CACX,CAAC;YACF,IACE,SAAS,KAAK,WAAW;gBACzB,kBAAkB,CAAC,aAAa,KAAK,QAAQ,EAC7C,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,SAAS,EAAE,6BAA6B;oBACxC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,YAAY;qBACnB;oBACD,GAAG,EAAE,mCAAmC,CAAC,kBAAkB,CAAC;iBAC7D,CAAC,CAAC;YACL,CAAC;iBAAM,IACL,SAAS,KAAK,UAAU;gBACxB,CAAC,kBAAkB,CAAC,aAAa,EACjC,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,YAAY;qBACnB;oBACD,OAAO,EAAE,kCAAkC,CAAC,kBAAkB,CAAC;iBAChE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,2CAA2C,CAClD,IAAkC;YAElC,MAAM,QAAQ,GAAG,oBAAoB,CAAC;YACtC,0DAA0D;YAC1D,IACE,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACjD,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACxD,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,QAAQ,GACZ,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC/C,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;gBACrB,CAAC,CAAC,qDAAqD;oBACpD,IAAI,CAAC,SAAS,CAAC,IAA4B,CAAC,IAAI,CAAC;YAExD,QAAQ,cAAc,EAAE,CAAC;gBACvB,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;wBACxB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,sBAAsB;4BACjC,IAAI,EAAE;gCACJ,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,QAAQ;6BACf;4BACD,OAAO,EAAE,kCAAkC,CAAC,IAAI,CAAC;yBAClD,CAAC,CAAC;oBACL,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,WAAW,CAAC,CAAC,CAAC;oBACjB,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACrD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,6BAA6B;4BACxC,IAAI,EAAE;gCACJ,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,QAAQ;6BACf;4BACD,GAAG,EAAE,mCAAmC,CAAC,IAAI,CAAC;yBAC/C,CAAC,CAAC;oBACL,CAAC;oBACD,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,8CAA8C,EAC5C,gCAAgC;YAClC,kDAAkD,EAChD,kCAAkC;YACpC,mBAAmB,EAAE,2CAA2C;SACjE,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}