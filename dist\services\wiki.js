/**
 * Axmol Wiki 服务
 * 负责从官方 Wiki 和文档源检索信息
 */
export class AxmolWikiService {
    AXMOL_WIKI_BASE = "https://github.com/axmolengine/axmol/wiki";
    AXMOL_DOCS_BASE = "https://axmolengine.github.io/axmol";
    /**
     * 获取精灵渲染相关文档
     */
    async getSpriteDocs(topic, language = "cpp") {
        try {
            // 搜索相关文档
            const searchResults = await this.searchDocumentation(topic);
            // 生成代码示例
            const codeExamples = this.generateSpriteExamples(topic, language);
            const content = `# Axmol 精灵渲染文档

## 搜索主题: ${topic}

## 文档内容

${searchResults}

## 代码示例 (${language.toUpperCase()})

${codeExamples}

## 相关 API

### 核心精灵类
- \`ax::Sprite\` - 主要的精灵类
- \`ax::SpriteFrame\` - 精灵帧
- \`ax::Texture2D\` - 纹理类
- \`ax::SpriteBatchNode\` - 批量渲染节点

### 常用方法
- \`Sprite::create()\` - 创建精灵
- \`Sprite::setTexture()\` - 设置纹理
- \`Sprite::setPosition()\` - 设置位置
- \`Sprite::setScale()\` - 设置缩放

## 最佳实践

1. **纹理管理**: 使用 TextureCache 管理纹理资源
2. **批量渲染**: 使用 SpriteBatchNode 提高性能
3. **内存优化**: 及时释放不需要的纹理资源
4. **坐标系统**: 理解 Axmol 的坐标系统 (左下角为原点)

## 更多资源

- [Axmol 官方 Wiki](${this.AXMOL_WIKI_BASE})
- [API 文档](${this.AXMOL_DOCS_BASE})
- [示例项目](https://github.com/axmolengine/axmol/tree/dev/tests)
`;
            return {
                content: [
                    {
                        type: "text",
                        text: content,
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `检索 Wiki 文档时发生错误: ${error instanceof Error ? error.message : String(error)}`,
                    },
                ],
                isError: true,
            };
        }
    }
    /**
     * 搜索文档内容
     */
    async searchDocumentation(topic) {
        try {
            // 模拟搜索结果，实际应用中会调用真实的 API
            const topics = {
                "sprite rendering": `
### 精灵渲染基础

Axmol 中的精灵渲染是游戏开发的核心功能之一。精灵 (Sprite) 是显示 2D 图像的基本组件。

#### 基本概念
- **精灵 (Sprite)**: 可以显示纹理的 2D 对象
- **纹理 (Texture)**: 图像数据，存储在 GPU 内存中
- **精灵帧 (SpriteFrame)**: 纹理的一部分，用于动画

#### 渲染流程
1. 加载纹理到内存
2. 创建精灵对象
3. 设置精灵属性 (位置、缩放、旋转等)
4. 添加到场景中进行渲染
`,
                "texture loading": `
### 纹理加载

纹理是精灵渲染的基础，正确的纹理管理对性能至关重要。

#### 支持格式
- PNG (推荐，支持透明)
- JPEG (适合不需要透明的图像)
- WEBP (现代格式，更好的压缩)
- PVR/ETC (移动平台优化格式)

#### 加载方式
- 同步加载: 适合小纹理
- 异步加载: 适合大纹理，避免阻塞主线程
- 预加载: 提前加载常用纹理
`,
                "default": `
### ${topic}

根据您的搜索主题，这里是相关的 Axmol 文档信息。

Axmol 是 Cocos2d-x 的现代化分支，提供了强大的 2D/3D 游戏开发能力。

#### 主要特性
- 跨平台支持 (iOS, Android, Windows, macOS, Linux)
- 现代 C++ 标准支持
- 高性能渲染引擎
- 丰富的组件系统
- Lua 脚本支持
`
            };
            return topics[topic.toLowerCase()] || topics.default;
        }
        catch (error) {
            return `搜索文档时发生错误: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
    /**
     * 生成代码示例
     */
    generateSpriteExamples(topic, language) {
        if (language === "cpp" || language === "all") {
            const cppExample = `
\`\`\`cpp
// 基本精灵创建和显示
#include "axmol.h"
USING_NS_AX;

class GameScene : public Scene
{
public:
    static Scene* createScene();
    virtual bool init();
    CREATE_FUNC(GameScene);
};

bool GameScene::init()
{
    if (!Scene::init())
        return false;

    // 获取屏幕尺寸
    auto visibleSize = Director::getInstance()->getVisibleSize();
    Vec2 origin = Director::getInstance()->getVisibleOrigin();

    // 创建精灵
    auto sprite = Sprite::create("HelloWorld.png");
    if (sprite)
    {
        // 设置位置到屏幕中心
        sprite->setPosition(Vec2(visibleSize.width/2 + origin.x, 
                                visibleSize.height/2 + origin.y));
        
        // 设置缩放
        sprite->setScale(0.5f);
        
        // 添加到场景
        this->addChild(sprite, 0);
    }

    // 使用纹理缓存
    auto textureCache = Director::getInstance()->getTextureCache();
    auto texture = textureCache->addImage("character.png");
    auto spriteFromTexture = Sprite::createWithTexture(texture);
    
    return true;
}
\`\`\``;
            if (language === "cpp") {
                return cppExample;
            }
        }
        if (language === "lua" || language === "all") {
            const luaExample = `
\`\`\`lua
-- Lua 精灵示例
local GameScene = class("GameScene", cc.Scene)

function GameScene:ctor()
    self.super.ctor(self)
end

function GameScene:onEnter()
    self.super.onEnter(self)
    
    -- 获取屏幕尺寸
    local visibleSize = cc.Director:getInstance():getVisibleSize()
    local origin = cc.Director:getInstance():getVisibleOrigin()
    
    -- 创建精灵
    local sprite = cc.Sprite:create("HelloWorld.png")
    if sprite then
        -- 设置位置
        sprite:setPosition(cc.p(visibleSize.width/2 + origin.x, 
                               visibleSize.height/2 + origin.y))
        
        -- 设置缩放
        sprite:setScale(0.5)
        
        -- 添加到场景
        self:addChild(sprite)
    end
end

return GameScene
\`\`\``;
            if (language === "lua") {
                return luaExample;
            }
        }
        if (language === "all") {
            return `
## C++ 示例
${this.generateSpriteExamples(topic, "cpp")}

## Lua 示例
${this.generateSpriteExamples(topic, "lua")}
`;
        }
        return "// 暂不支持该语言的示例";
    }
}
//# sourceMappingURL=wiki.js.map