import { CallToolResult } from "@modelcontextprotocol/sdk/types.js";
/**
 * Axmol 代码生成服务
 * 负责生成各种类型的 Axmol 代码示例
 */
export declare class AxmolCodeGenerator {
    /**
     * 生成角色移动代码
     */
    generateCharacterMovement(movementType: string, characterType: string): Promise<CallToolResult>;
    /**
     * 生成具体的移动代码实现
     */
    private generateMovementCode;
    /**
     * 获取角色成员变量
     */
    private getCharacterMemberVariables;
    /**
     * 获取移动相关成员变量
     */
    private getMovementMemberVariables;
    /**
     * 生成初始化方法
     */
    private generateInitMethod;
    /**
     * 获取角色初始化代码
     */
    private getCharacterInitCode;
    /**
     * 获取移动系统初始化代码
     */
    private getMovementInitCode;
    /**
     * 生成更新方法
     */
    private generateUpdateMethod;
    /**
     * 获取更新逻辑
     */
    private getUpdateLogic;
    /**
     * 生成输入处理代码
     */
    private generateInputHandling;
}
//# sourceMappingURL=codeGenerator.d.ts.map