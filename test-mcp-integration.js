#!/usr/bin/env node

/**
 * 测试完整的MCP服务器集成功能
 * 验证官方知识库是否成功集成到MCP工具中
 */

import { spawn } from 'child_process';
import { writeFileSync, readFileSync } from 'fs';

/**
 * 测试MCP服务器的智能助手功能
 */
async function testMCPServer() {
  console.log('🧪 开始测试MCP服务器集成功能...\n');
  
  const testCases = [
    {
      name: "Sprite创建相关查询",
      question: "如何在Axmol中创建一个精灵？",
      context: "我是新手开发者，想了解基本的精灵创建方法",
      includeCode: true,
      language: "cpp"
    },
    {
      name: "Director类相关查询",
      question: "Axmol中的Director类有什么作用？",
      context: "想了解游戏引擎的核心管理类",
      includeCode: false,
      language: "cpp"
    },
    {
      name: "事件处理查询",
      question: "如何处理键盘事件？",
      context: "需要在游戏中响应用户的键盘输入",
      includeCode: true,
      language: "cpp"
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📋 测试案例: ${testCase.name}`);
    console.log(`问题: ${testCase.question}`);
    console.log(`上下文: ${testCase.context}`);
    
    try {
      const result = await callMCPTool(testCase);
      
      if (result.success) {
        console.log(`✅ MCP调用成功`);
        console.log(`📊 响应长度: ${result.response.length} 字符`);
        
        // 检查响应是否包含官方知识库内容
        const hasOfficialDocs = result.response.includes('官方API文档') || 
                               result.response.includes('官方知识库') ||
                               result.response.includes('axmol.dev');
        
        if (hasOfficialDocs) {
          console.log(`🏛️ ✅ 包含官方知识库内容`);
        } else {
          console.log(`⚠️ 未检测到官方知识库内容`);
        }
        
        // 检查响应是否包含API文档链接
        const hasApiLinks = result.response.includes('https://axmol.dev/manual/latest/');
        if (hasApiLinks) {
          console.log(`🔗 ✅ 包含API文档链接`);
        }
        
        // 显示响应摘要
        const summary = result.response.substring(0, 300) + '...';
        console.log(`📄 响应摘要: ${summary}`);
        
      } else {
        console.log(`❌ MCP调用失败: ${result.error}`);
      }
      
    } catch (error) {
      console.log(`❌ 测试失败:`, error.message);
    }
  }
  
  console.log('\n✅ MCP服务器集成测试完成！');
}

/**
 * 调用MCP工具
 */
async function callMCPTool(testCase) {
  return new Promise((resolve) => {
    try {
      // 创建MCP请求
      const mcpRequest = {
        jsonrpc: "2.0",
        id: 1,
        method: "tools/call",
        params: {
          name: "axmol_smart_assistant",
          arguments: {
            question: testCase.question,
            context: testCase.context,
            includeCode: testCase.includeCode,
            language: testCase.language
          }
        }
      };
      
      // 启动MCP服务器进程
      const mcpProcess = spawn('node', ['dist/index.js'], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
      });
      
      let responseData = '';
      let errorData = '';
      
      // 设置超时
      const timeout = setTimeout(() => {
        mcpProcess.kill();
        resolve({
          success: false,
          error: 'MCP调用超时'
        });
      }, 60000); // 60秒超时
      
      mcpProcess.stdout.on('data', (data) => {
        responseData += data.toString();
      });
      
      mcpProcess.stderr.on('data', (data) => {
        errorData += data.toString();
      });
      
      mcpProcess.on('close', (code) => {
        clearTimeout(timeout);
        
        if (code === 0 && responseData) {
          try {
            // 解析MCP响应
            const lines = responseData.split('\n').filter(line => line.trim());
            let mcpResponse = null;
            
            for (const line of lines) {
              try {
                const parsed = JSON.parse(line);
                if (parsed.result && parsed.result.content) {
                  mcpResponse = parsed;
                  break;
                }
              } catch (e) {
                // 忽略解析错误，继续尝试下一行
              }
            }
            
            if (mcpResponse && mcpResponse.result && mcpResponse.result.content) {
              const content = mcpResponse.result.content[0];
              resolve({
                success: true,
                response: content.text || content.content || '无响应内容'
              });
            } else {
              resolve({
                success: false,
                error: '无法解析MCP响应'
              });
            }
          } catch (error) {
            resolve({
              success: false,
              error: `响应解析失败: ${error.message}`
            });
          }
        } else {
          resolve({
            success: false,
            error: `MCP进程退出码: ${code}, 错误: ${errorData}`
          });
        }
      });
      
      mcpProcess.on('error', (error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          error: `MCP进程启动失败: ${error.message}`
        });
      });
      
      // 发送MCP请求
      mcpProcess.stdin.write(JSON.stringify(mcpRequest) + '\n');
      mcpProcess.stdin.end();
      
    } catch (error) {
      resolve({
        success: false,
        error: `MCP调用异常: ${error.message}`
      });
    }
  });
}

// 运行测试
testMCPServer().catch(console.error);
