{"version": 3, "file": "comma-dangle.js", "sourceRoot": "", "sources": ["../../src/rules/comma-dangle.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAMtE,kCAAmD;AACnD,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,cAAc,CAAC,CAAC;AAUnD,MAAM,mBAAmB,GAAG;IAC1B,kBAAkB;IAClB,QAAQ;IACR,OAAO;IACP,gBAAgB;CACjB,CAAC;AAEF,MAAM,oBAAoB,GAAG,OAAO,CAAC;AAErC,SAAS,gBAAgB,CAAC,OAAe;IACvC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAO;YACL,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE,OAAO;SAChB,CAAC;IACJ,CAAC;IACD,OAAO;QACL,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,oBAAoB;QAC5C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,oBAAoB;QAClD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,oBAAoB;KAC/C,CAAC;AACJ,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,CAAC,4BAA4B,CAAC;QAC1C,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,qCAAqC;YAClD,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE;YACN,KAAK,EAAE;gBACL,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,mBAAmB;iBAC1B;gBACD,eAAe,EAAE;oBACf,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,GAAG,mBAAmB,EAAE,QAAQ,CAAC;iBACzC;aACF;YACD,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL;oBACE,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,eAAe;yBACtB;wBACD;4BACE,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,MAAM,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC3C,OAAO,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC5C,OAAO,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC5C,OAAO,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC5C,SAAS,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC9C,KAAK,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC1C,QAAQ,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;gCAC7C,MAAM,EAAE,EAAE,IAAI,EAAE,yBAAyB,EAAE;6BAC5C;4BACD,oBAAoB,EAAE,KAAK;yBAC5B;qBACF;iBACF;aACF;YACD,eAAe,EAAE,KAAK;SACvB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,OAAO,CAAC;IACzB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEpD,MAAM,SAAS,GAAG;YAChB,MAAM,EAAE,UAAU;YAClB,kBAAkB,EAAE,qBAAqB;YACzC,gBAAgB,EAAE,qBAAqB;YACvC,KAAK,EAAE,WAAW;YAClB,qEAAqE;YACrE,kHAAkH;YAClH,MAAM,EAAE,GAAG,EAAE,GAAE,CAAC;SACjB,CAAC;QAEF,SAAS,IAAI,CAAC,KAAsB;YAClC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;QACzC,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAc,CAAC,iBAAiB;oBACnC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,KAAK,sBAAc,CAAC,0BAA0B;oBAC5C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC3B,KAAK,sBAAc,CAAC,WAAW;oBAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjC;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAmB;YAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,IAAI,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACxD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChD,OAAO,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;QACxD,CAAC;QAED,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAA,mBAAY,EAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/C,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,YAAY;oBACvB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,UAAU,CAAC,IAAmB;YACrC,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAA,mBAAY,EAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,SAAS;oBACpB,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBAC1C,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAmB;YAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,WAAW,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAmB;YAChD,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,UAAU,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO;YACL,GAAG,KAAK;YACR,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC;YACrD,0BAA0B,EAAE,SAAS,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YACjE,WAAW,EAAE,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC;SACjD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}