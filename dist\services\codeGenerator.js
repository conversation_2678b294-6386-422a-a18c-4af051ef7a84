/**
 * Axmol 代码生成服务
 * 负责生成各种类型的 Axmol 代码示例
 */
export class AxmolCodeGenerator {
    /**
     * 生成角色移动代码
     */
    async generateCharacterMovement(movementType, characterType) {
        try {
            const codeExample = this.generateMovementCode(movementType, characterType);
            const content = `# Axmol 2.6.0 角色移动代码示例

## 配置信息
- **移动类型**: ${movementType}
- **角色类型**: ${characterType}
- **引擎版本**: Axmol 2.6.0

## 完整代码实现

${codeExample}

## 使用说明

1. 将代码添加到您的场景类中
2. 确保已正确设置输入监听器
3. 根据需要调整移动速度和参数
4. 测试在目标平台上的表现

## 性能优化建议

- 使用对象池管理频繁创建的对象
- 合理设置帧率和更新频率
- 在移动设备上注意电量消耗
- 使用批量渲染优化绘制性能

## 扩展功能

- 添加移动动画效果
- 实现平滑的移动插值
- 添加碰撞检测
- 实现寻路算法
`;
            return {
                content: [
                    {
                        type: "text",
                        text: content,
                    },
                ],
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `生成代码时发生错误: ${error instanceof Error ? error.message : String(error)}`,
                    },
                ],
                isError: true,
            };
        }
    }
    /**
     * 生成具体的移动代码实现
     */
    generateMovementCode(movementType, characterType) {
        const baseIncludes = `
\`\`\`cpp
#include "axmol.h"
USING_NS_AX;

class CharacterController : public Node
{
public:
    static CharacterController* create();
    virtual bool init();
    
    // 移动相关方法
    void startMovement();
    void stopMovement();
    void updatePosition(float deltaTime);
    
private:
    ${this.getCharacterMemberVariables(characterType)}
    ${this.getMovementMemberVariables(movementType)}
};
\`\`\``;
        const initMethod = this.generateInitMethod(movementType, characterType);
        const updateMethod = this.generateUpdateMethod(movementType, characterType);
        const inputHandling = this.generateInputHandling(movementType);
        return `${baseIncludes}

## 初始化方法

${initMethod}

## 更新方法

${updateMethod}

## 输入处理

${inputHandling}`;
    }
    /**
     * 获取角色成员变量
     */
    getCharacterMemberVariables(characterType) {
        switch (characterType) {
            case "sprite":
                return `
    Sprite* m_character;
    Vec2 m_position;
    float m_speed;`;
            case "node":
                return `
    Node* m_character;
    Vec2 m_position;
    float m_speed;`;
            case "physics_body":
                return `
    Sprite* m_character;
    PhysicsBody* m_physicsBody;
    Vec2 m_velocity;
    float m_force;`;
            default:
                return `
    Node* m_character;
    Vec2 m_position;
    float m_speed;`;
        }
    }
    /**
     * 获取移动相关成员变量
     */
    getMovementMemberVariables(movementType) {
        switch (movementType) {
            case "keyboard":
                return `
    bool m_keyPressed[4]; // W, A, S, D
    EventListenerKeyboard* m_keyboardListener;`;
            case "touch":
                return `
    Vec2 m_touchStartPos;
    Vec2 m_touchCurrentPos;
    bool m_isTouching;
    EventListenerTouchOneByOne* m_touchListener;`;
            case "physics":
                return `
    PhysicsWorld* m_physicsWorld;
    Vec2 m_targetVelocity;`;
            case "pathfinding":
                return `
    std::vector<Vec2> m_path;
    int m_currentPathIndex;
    Vec2 m_targetPosition;`;
            default:
                return `
    Vec2 m_targetPosition;
    bool m_isMoving;`;
        }
    }
    /**
     * 生成初始化方法
     */
    generateInitMethod(movementType, characterType) {
        return `
\`\`\`cpp
bool CharacterController::init()
{
    if (!Node::init())
        return false;

    // 初始化角色
    ${this.getCharacterInitCode(characterType)}
    
    // 初始化移动系统
    ${this.getMovementInitCode(movementType)}
    
    // 开始更新循环
    this->scheduleUpdate();
    
    return true;
}

CharacterController* CharacterController::create()
{
    CharacterController* ret = new CharacterController();
    if (ret && ret->init())
    {
        ret->autorelease();
        return ret;
    }
    CC_SAFE_DELETE(ret);
    return nullptr;
}
\`\`\``;
    }
    /**
     * 获取角色初始化代码
     */
    getCharacterInitCode(characterType) {
        switch (characterType) {
            case "sprite":
                return `
    m_character = Sprite::create("character.png");
    if (m_character)
    {
        this->addChild(m_character);
        m_position = Vec2(100, 100);
        m_character->setPosition(m_position);
    }
    m_speed = 200.0f; // 像素/秒`;
            case "node":
                return `
    m_character = Node::create();
    auto sprite = Sprite::create("character.png");
    m_character->addChild(sprite);
    this->addChild(m_character);
    m_position = Vec2(100, 100);
    m_character->setPosition(m_position);
    m_speed = 200.0f;`;
            case "physics_body":
                return `
    m_character = Sprite::create("character.png");
    if (m_character)
    {
        // 创建物理体
        auto physicsBody = PhysicsBody::createBox(m_character->getContentSize());
        physicsBody->setDynamic(true);
        physicsBody->setMass(1.0f);
        physicsBody->setLinearDamping(0.5f);
        m_character->setPhysicsBody(physicsBody);
        m_physicsBody = physicsBody;
        
        this->addChild(m_character);
        m_character->setPosition(Vec2(100, 100));
    }
    m_force = 500.0f;`;
            default:
                return `
    m_character = Node::create();
    this->addChild(m_character);
    m_position = Vec2(100, 100);
    m_speed = 200.0f;`;
        }
    }
    /**
     * 获取移动系统初始化代码
     */
    getMovementInitCode(movementType) {
        switch (movementType) {
            case "keyboard":
                return `
    // 初始化键盘状态
    for (int i = 0; i < 4; i++)
        m_keyPressed[i] = false;
    
    // 设置键盘监听器
    m_keyboardListener = EventListenerKeyboard::create();
    m_keyboardListener->onKeyPressed = [this](EventKeyboard::KeyCode keyCode, Event* event) {
        switch (keyCode) {
            case EventKeyboard::KeyCode::KEY_W: m_keyPressed[0] = true; break;
            case EventKeyboard::KeyCode::KEY_A: m_keyPressed[1] = true; break;
            case EventKeyboard::KeyCode::KEY_S: m_keyPressed[2] = true; break;
            case EventKeyboard::KeyCode::KEY_D: m_keyPressed[3] = true; break;
        }
    };
    m_keyboardListener->onKeyReleased = [this](EventKeyboard::KeyCode keyCode, Event* event) {
        switch (keyCode) {
            case EventKeyboard::KeyCode::KEY_W: m_keyPressed[0] = false; break;
            case EventKeyboard::KeyCode::KEY_A: m_keyPressed[1] = false; break;
            case EventKeyboard::KeyCode::KEY_S: m_keyPressed[2] = false; break;
            case EventKeyboard::KeyCode::KEY_D: m_keyPressed[3] = false; break;
        }
    };
    Director::getInstance()->getEventDispatcher()->addEventListenerWithSceneGraphPriority(m_keyboardListener, this);`;
            case "touch":
                return `
    m_isTouching = false;
    
    // 设置触摸监听器
    m_touchListener = EventListenerTouchOneByOne::create();
    m_touchListener->onTouchBegan = [this](Touch* touch, Event* event) -> bool {
        m_touchStartPos = touch->getLocation();
        m_touchCurrentPos = m_touchStartPos;
        m_isTouching = true;
        return true;
    };
    m_touchListener->onTouchMoved = [this](Touch* touch, Event* event) {
        m_touchCurrentPos = touch->getLocation();
    };
    m_touchListener->onTouchEnded = [this](Touch* touch, Event* event) {
        m_isTouching = false;
    };
    Director::getInstance()->getEventDispatcher()->addEventListenerWithSceneGraphPriority(m_touchListener, this);`;
            case "physics":
                return `
    // 获取物理世界
    auto scene = Director::getInstance()->getRunningScene();
    m_physicsWorld = scene->getPhysicsWorld();
    m_targetVelocity = Vec2::ZERO;`;
            case "pathfinding":
                return `
    m_currentPathIndex = 0;
    m_targetPosition = Vec2::ZERO;`;
            default:
                return `// 基本移动初始化`;
        }
    }
    /**
     * 生成更新方法
     */
    generateUpdateMethod(movementType, characterType) {
        return `
\`\`\`cpp
void CharacterController::update(float deltaTime)
{
    Node::update(deltaTime);
    
    ${this.getUpdateLogic(movementType, characterType)}
}
\`\`\``;
    }
    /**
     * 获取更新逻辑
     */
    getUpdateLogic(movementType, characterType) {
        switch (movementType) {
            case "keyboard":
                if (characterType === "physics_body") {
                    return `
    // 物理体键盘移动
    Vec2 force = Vec2::ZERO;
    if (m_keyPressed[0]) force.y += m_force; // W
    if (m_keyPressed[1]) force.x -= m_force; // A
    if (m_keyPressed[2]) force.y -= m_force; // S
    if (m_keyPressed[3]) force.x += m_force; // D
    
    if (force.length() > 0)
    {
        m_physicsBody->applyForce(force);
    }`;
                }
                else {
                    return `
    // 基本键盘移动
    Vec2 movement = Vec2::ZERO;
    if (m_keyPressed[0]) movement.y += 1; // W
    if (m_keyPressed[1]) movement.x -= 1; // A
    if (m_keyPressed[2]) movement.y -= 1; // S
    if (m_keyPressed[3]) movement.x += 1; // D
    
    if (movement.length() > 0)
    {
        movement.normalize();
        movement *= m_speed * deltaTime;
        m_position += movement;
        m_character->setPosition(m_position);
    }`;
                }
            case "touch":
                return `
    // 触摸移动
    if (m_isTouching)
    {
        Vec2 direction = m_touchCurrentPos - m_character->getPosition();
        float distance = direction.length();
        
        if (distance > 10.0f) // 最小移动距离
        {
            direction.normalize();
            Vec2 movement = direction * m_speed * deltaTime;
            
            // 限制移动距离，避免超过目标
            if (movement.length() > distance)
                movement = direction * distance;
                
            m_position += movement;
            m_character->setPosition(m_position);
        }
    }`;
            case "physics":
                return `
    // 物理移动 (使用速度控制)
    if (m_targetVelocity.length() > 0)
    {
        m_physicsBody->setVelocity(m_targetVelocity);
    }`;
            case "pathfinding":
                return `
    // 路径寻找移动
    if (m_currentPathIndex < m_path.size())
    {
        Vec2 target = m_path[m_currentPathIndex];
        Vec2 direction = target - m_character->getPosition();
        float distance = direction.length();
        
        if (distance < 5.0f) // 到达当前路径点
        {
            m_currentPathIndex++;
        }
        else
        {
            direction.normalize();
            Vec2 movement = direction * m_speed * deltaTime;
            m_position += movement;
            m_character->setPosition(m_position);
        }
    }`;
            default:
                return `
    // 基本移动逻辑
    updatePosition(deltaTime);`;
        }
    }
    /**
     * 生成输入处理代码
     */
    generateInputHandling(movementType) {
        switch (movementType) {
            case "keyboard":
                return `
\`\`\`cpp
// 键盘输入处理已在初始化中设置
// 支持 WASD 键控制移动
// W - 向上, A - 向左, S - 向下, D - 向右
\`\`\``;
            case "touch":
                return `
\`\`\`cpp
// 触摸输入处理
// 角色会朝着触摸点移动
// 支持拖拽移动
\`\`\``;
            case "physics":
                return `
\`\`\`cpp
// 物理移动控制方法
void CharacterController::setTargetVelocity(const Vec2& velocity)
{
    m_targetVelocity = velocity;
}

void CharacterController::applyImpulse(const Vec2& impulse)
{
    if (m_physicsBody)
    {
        m_physicsBody->applyImpulse(impulse);
    }
}
\`\`\``;
            case "pathfinding":
                return `
\`\`\`cpp
// 路径设置方法
void CharacterController::setPath(const std::vector<Vec2>& path)
{
    m_path = path;
    m_currentPathIndex = 0;
}

void CharacterController::moveToPosition(const Vec2& position)
{
    // 简单的直线路径
    m_path.clear();
    m_path.push_back(position);
    m_currentPathIndex = 0;
}
\`\`\``;
            default:
                return `
\`\`\`cpp
// 自定义输入处理
\`\`\``;
        }
    }
}
//# sourceMappingURL=codeGenerator.js.map