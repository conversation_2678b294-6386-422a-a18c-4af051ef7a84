interface Tool {
    name: string;
    description: string;
    inputSchema: any;
}
interface CallToolResult {
    content: Array<{
        type: string;
        text: string;
    }>;
    isError?: boolean;
}
/**
 * Axmol MCP 服务器核心类
 * 统一管理所有 Axmol 相关的功能和服务
 */
export declare class AxmolMCPServer {
    constructor();
    /**
     * 获取所有可用的工具列表
     */
    getTools(): Promise<Tool[]>;
    /**
     * 处理工具调用
     */
    handleToolCall(params: any): Promise<CallToolResult>;
    private getSpriteDocs;
    private generateCharacterMovement;
    private getMovementSpecificMembers;
    private getMovementInitCode;
    private getMovementUpdateCode;
}
export {};
//# sourceMappingURL=server.d.ts.map