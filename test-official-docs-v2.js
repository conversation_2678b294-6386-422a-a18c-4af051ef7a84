#!/usr/bin/env node

/**
 * 测试官方知识库集成功能 V2
 * 使用内容抓取方式而不是搜索API
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

const AXMOL_OFFICIAL_DOCS = "https://axmol.dev/manual/latest";

/**
 * 搜索官方知识库主页
 */
async function searchOfficialMainPage(searchTerms) {
  try {
    console.log('📥 获取官方知识库主页...');
    const response = await axios.get(AXMOL_OFFICIAL_DOCS, {
      timeout: 8000,
      headers: {
        'User-Agent': 'Axmol-MCP-Test',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });

    const $ = cheerio.load(response.data);
    const title = 'Axmol Engine 官方API文档';
    const content = $('body').text();
    
    // 检查相关性
    let relevanceScore = 0;
    const contentLower = content.toLowerCase();
    
    searchTerms.forEach(term => {
      const termLower = term.toLowerCase();
      if (contentLower.includes(termLower)) {
        relevanceScore += 3;
      }
    });

    // 总是包含主页作为基础信息
    return {
      type: 'official_docs',
      title,
      url: AXMOL_OFFICIAL_DOCS,
      content: content.substring(0, 1500),
      relevanceScore: relevanceScore + 1, // 基础分数
      matchedTerms: searchTerms.filter(term => 
        contentLower.includes(term.toLowerCase())
      ),
      isMainPage: true
    };

  } catch (error) {
    console.log('⚠️ 获取官方知识库主页失败:', error.message);
    return null;
  }
}

/**
 * 通过网络搜索发现相关的API页面
 */
async function discoverRelevantApiPages(searchTerms) {
  const discoveredPages = [];

  try {
    console.log('🔍 通过网络搜索发现相关API页面...');

    // 基于搜索词生成潜在的API页面路径
    const classNameMappings = {
      'sprite': ['sprite', 'spritebatchnode', 'spriteframe'],
      'node': ['node'],
      'scene': ['scene', 'scenemgr'],
      'director': ['director'],
      'action': ['action', 'actionmanager', 'actionease'],
      'animation': ['animation', 'animate'],
      'event': ['event', 'eventlistener', 'eventdispatcher'],
      'touch': ['touch', 'eventtouch'],
      'keyboard': ['keyboard', 'eventkeyboard'],
      'mouse': ['mouse', 'eventmouse'],
      'physics': ['physicsbody', 'physicsworld', 'physicsshape'],
      'audio': ['audio', 'audioengine'],
      'ui': ['widget', 'button', 'label', 'textfield'],
      'render': ['renderer', 'rendercommand'],
      'texture': ['texture2d', 'textureatlasnode'],
      'camera': ['camera'],
      'light': ['light', 'ambientlight', 'directionallight'],
      'mesh': ['mesh', 'meshmaterial', 'meshvertexdata'],
      '3d': ['sprite3d', 'mesh', 'camera', 'light']
    };

    // 为每个搜索词查找对应的类名
    searchTerms.forEach(term => {
      const termLower = term.toLowerCase();

      // 直接匹配
      if (classNameMappings[termLower]) {
        classNameMappings[termLower].forEach(className => {
          discoveredPages.push(`classax_1_1_${className}.html`);
        });
      }

      // 部分匹配
      Object.entries(classNameMappings).forEach(([key, classNames]) => {
        if (key.includes(termLower) || termLower.includes(key)) {
          classNames.forEach(className => {
            discoveredPages.push(`classax_1_1_${className}.html`);
          });
        }
      });
    });

    const uniquePages = [...new Set(discoveredPages)];
    console.log(`✅ 发现 ${uniquePages.length} 个潜在的API页面`);
    return uniquePages;

  } catch (error) {
    console.log('⚠️ API页面发现失败:', error.message);
    return [];
  }
}

/**
 * 带重试机制的API页面搜索
 */
async function searchApiPageWithRetry(page, searchTerms, maxRetries = 2) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const pageUrl = `${AXMOL_OFFICIAL_DOCS}/${page}`;

      // 根据尝试次数调整超时时间
      const timeout = 5000 + (attempt - 1) * 2000; // 5s, 7s, 9s

      console.log(`📥 获取 (尝试 ${attempt}): ${pageUrl}`);

      const response = await axios.get(pageUrl, {
        timeout,
        headers: {
          'User-Agent': 'Axmol-MCP-Test',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Cache-Control': 'no-cache'
        },
        maxContentLength: 1024 * 1024 * 2, // 2MB限制
        validateStatus: (status) => status === 200
      });

      const $ = cheerio.load(response.data);
      const title = $('title').text() || $('h1').first().text() || page;
      const content = $('body').text();

      // 检查页面是否有效
      if (content.includes('Page not found') || content.includes('404') || content.length < 100) {
        console.log(`⚪ 页面无效或内容过少: ${page}`);
        return null;
      }

      // 检查相关性
      let relevanceScore = 0;
      const contentLower = content.toLowerCase();
      const titleLower = title.toLowerCase();

      searchTerms.forEach(term => {
        const termLower = term.toLowerCase();
        if (titleLower.includes(termLower)) {
          relevanceScore += 10;
        }
        if (contentLower.includes(termLower)) {
          relevanceScore += 5;
        }
      });

      if (relevanceScore > 0) {
        console.log(`✅ 找到相关API页面: ${title} (分数: ${relevanceScore})`);
        return {
          type: 'official_docs',
          title,
          url: pageUrl,
          content: content.substring(0, 2000),
          relevanceScore,
          matchedTerms: searchTerms.filter(term =>
            contentLower.includes(term.toLowerCase()) || titleLower.includes(term.toLowerCase())
          ),
          source: 'api_page'
        };
      } else {
        console.log(`⚪ API页面不相关: ${title}`);
        return null;
      }

    } catch (error) {
      const errorMsg = error.message;

      if (attempt === maxRetries) {
        if (errorMsg.includes('404')) {
          console.log(`⚪ API页面不存在: ${page}`);
        } else if (errorMsg.includes('timeout')) {
          console.log(`⏰ API页面请求超时: ${page}`);
        } else {
          console.log(`❌ API页面请求失败: ${page} - ${errorMsg}`);
        }
        return null;
      } else {
        console.log(`⚠️ API页面请求失败 (尝试 ${attempt}/${maxRetries}): ${page} - ${errorMsg}`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  return null;
}

/**
 * 搜索官方知识库
 */
async function searchOfficialDocs(searchTerms, categories) {
  const results = [];

  try {
    console.log('🔍 搜索官方知识库...');

    // 1. 首先获取主页内容
    const mainPageResult = await searchOfficialMainPage(searchTerms);
    if (mainPageResult) {
      results.push(mainPageResult);
    }

    // 2. 通过网络搜索发现相关的API页面
    const discoveredPages = await discoverRelevantApiPages(searchTerms);

    // 3. 合并已知的常见API页面
    const knownApiPages = [
      'd4/d72/classax_1_1_director.html',  // Director类
      'd5/db6/classax_1_1_event.html',     // Event类
      'd1/dfb/classax_1_1_mesh_material.html', // MeshMaterial类
      'd7/d27/classax_1_1_event_keyboard.html', // EventKeyboard类
      'd0/d33/classax_1_1_ease_cubic_action_in.html' // EaseCubicActionIn类
    ];

    const allApiPages = [...new Set([...discoveredPages, ...knownApiPages])];

    console.log(`📋 尝试搜索 ${allApiPages.length} 个API页面...`);

    // 使用批量处理和错误恢复策略
    const apiSearchPromises = allApiPages.map(async (page, index) => {
      // 添加延迟以避免过多并发请求
      await new Promise(resolve => setTimeout(resolve, index * 100));

      return await searchApiPageWithRetry(page, searchTerms, 2); // 最多重试2次
    });

    const apiResults = await Promise.allSettled(apiSearchPromises);
    apiResults.forEach((result) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      }
    });

    // 按相关性排序并限制数量
    const sortedResults = results
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 8); // 限制最多8个结果

    console.log(`✅ 官方知识库搜索完成: 找到 ${sortedResults.length} 个相关结果`);
    return sortedResults;

  } catch (error) {
    console.log('⚠️ 官方知识库搜索失败:', error.message);
    return [];
  }
}

/**
 * 测试官方知识库搜索
 */
async function testOfficialDocsSearch() {
  console.log('🧪 开始测试官方知识库搜索功能 V2...\n');
  
  const testCases = [
    {
      name: "Sprite 相关搜索",
      searchTerms: ["sprite", "create"],
      categories: ["sprite"]
    },
    {
      name: "Node 相关搜索", 
      searchTerms: ["node", "addchild"],
      categories: ["scene"]
    },
    {
      name: "General 搜索",
      searchTerms: ["axmol", "engine"],
      categories: []
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📋 测试案例: ${testCase.name}`);
    console.log(`搜索词: ${testCase.searchTerms.join(', ')}`);
    console.log(`类别: ${testCase.categories.join(', ')}`);
    
    try {
      const results = await searchOfficialDocs(testCase.searchTerms, testCase.categories);
      
      console.log(`\n🎯 找到 ${results.length} 个相关结果:`);
      results.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.title}`);
        console.log(`   相关性分数: ${result.relevanceScore}`);
        console.log(`   匹配关键词: ${result.matchedTerms.join(', ')}`);
        console.log(`   📖 文档链接: ${result.url}`);
        if (result.isMainPage) {
          console.log(`   📍 类型: 主页`);
        }
        console.log(`   📄 内容预览: ${result.content.substring(0, 200)}...`);
      });
      
    } catch (error) {
      console.log(`❌ 测试失败:`, error.message);
    }
  }
  
  console.log('\n✅ 测试完成！');
}

// 运行测试
testOfficialDocsSearch().catch(console.error);
